import path from "path"
import tailwindcss from "@tailwindcss/vite"
import react from '@vitejs/plugin-react'
import { defineConfig, loadEnv } from 'vite'

// https://vite.dev/config/
export default defineConfig(({ mode }) => {
  // Load env file based on `mode` in the current directory.
  // Set the third parameter to '' to load all env regardless of the `VITE_` prefix.
  loadEnv(mode, process.cwd(), '')

  // 开发环境和生产环境使用不同的代理配置
  // 检测是否在Docker环境中运行
  const isDockerEnv = process.env.DOCKER_ENV === 'true';

  console.log('Docker环境检测:', {
    DOCKER_ENV: process.env.DOCKER_ENV,
    isDockerEnv: isDockerEnv
  });

  const proxyConfig = {
    // 添加对 /api 路径的代理
    '/api': {
      target: isDockerEnv ? 'http://backend:8000' : 'http://localhost:8000',
      changeOrigin: true,
      secure: false,
      rewrite: (path: string) => path.replace(/^\/api/, "/v1/api"),
      // 禁用重定向，让代理服务器处理重定向而不是浏览器
      followRedirects: true,
      // 确保代理服务器处理所有请求，包括重定向
      selfHandleResponse: false
    },
    // 添加对 /v1/api 路径的代理 (直接路径)
    '/v1/api': {
      target: isDockerEnv ? 'http://backend:8000' : 'http://localhost:8000',
      changeOrigin: true,
      secure: false,
      // 禁用重定向，让代理服务器处理重定向而不是浏览器
      followRedirects: true,
      // 确保代理服务器处理所有请求，包括重定向
      selfHandleResponse: false
    },
    // 添加对 /auth 路径的直接代理 (用于登录)
    '/auth': {
      target: isDockerEnv ? 'http://backend:8000/v1/api' : 'http://localhost:8000/v1/api',
      changeOrigin: true,
      secure: false,
      // 禁用重定向，让代理服务器处理重定向而不是浏览器
      followRedirects: true,
      // 确保代理服务器处理所有请求，包括重定向
      selfHandleResponse: false
    },
    // 添加对 PDF 服务的代理
    '/pdf-service': {
      target: isDockerEnv ? 'http://pdf_service:8002' : 'http://localhost:8002',
      changeOrigin: true,
      secure: false,
      rewrite: (p: string) => p.replace(/^\/pdf-service/, ''),
      // 禁用重定向，让代理服务器处理重定向而不是浏览器
      followRedirects: true,
      // 确保代理服务器处理所有请求，包括重定向
      selfHandleResponse: false
    },
    // 添加对 backend:8000 的直接代理 (用于处理硬编码的 URL)
    'http://backend:8000': {
      target: 'http://backend:8000',
      changeOrigin: true,
      secure: false,
      rewrite: (path: string) => path.replace(/^http:\/\/backend:8000/, ""),
      // 禁用重定向，让代理服务器处理重定向而不是浏览器
      followRedirects: true,
      // 确保代理服务器处理所有请求，包括重定向
      selfHandleResponse: false
    }
  };

  // 打印代理配置
  console.log('代理配置:', proxyConfig);

  return {
    plugins: [react(), tailwindcss()],
    resolve: {
      alias: {
        "@": path.resolve(__dirname, "./src"),
      },
    },
    optimizeDeps: {
      // 强制重新构建依赖
      force: true,
      // 包含需要预构建的依赖
      include: [
        'react',
        'react-dom',
        'react-router-dom',
        '@radix-ui/react-dialog',
        '@radix-ui/react-dropdown-menu',
        '@radix-ui/react-slot',
        'lucide-react',
        'class-variance-authority',
        'clsx',
        'tailwind-merge'
      ]
    },
    server: {
      port: 5173,
      host: true, // 监听所有地址，包括局域网和公网地址
      proxy: proxyConfig,
      allowedHosts: ["1bee-240e-389-5dba-900-3646-ecff-fe66-355a.ngrok-free.app"]
    },
    build: {
      outDir: 'dist',
      sourcemap: false,
      // Reduce chunk size warnings
      chunkSizeWarningLimit: 1600,
    }
  }
})
