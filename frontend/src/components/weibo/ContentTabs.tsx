import { Ava<PERSON>, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON>, Card<PERSON>ontent, CardFooter, CardHeader } from "@/components/ui/card"
import {
  MessageSquare,
  ThumbsUp,
  BotMessageSquare,
  SmilePlus,
  Image,
  Send,
  X,
  Brain,
  ChevronDown,
  ChevronRight,
  ChevronUp,
  Pencil,
  Trash2,
  Arrow<PERSON><PERSON>,
  <PERSON><PERSON>,
  Check,
  ZoomIn
} from "lucide-react"
import { ImagePreview } from "./ImagePreview"

import { Textarea } from "@/components/ui/textarea"
import { toast } from "sonner"
import { useEffect, useState, useRef } from "react"
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import remarkMath from 'remark-math'
import rehypeKatex from 'rehype-katex'
import { MarkdownComponents } from '@/components/markdown/MarkdownComponents'
import { useLLMStore } from '@/store/llmStore'
import API from '@/config/api'
import { convertSupabaseUrl, convertUrlForDocker } from '@/utils/url-utils'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import 'katex/dist/katex.min.css'

// 定义数据类型
// 对话消息类型
interface ChatMessage {
  role: 'user' | 'assistant';
  content: string;
}
interface UserInfo {
  id: string;
  username?: string;
  display_name?: string;
  email?: string;
  thumbnail?: string;
}

interface Post {
  post: {
    id: string;
    content: string;
    timestamp: string;
    owner_id: string;
  };
  images: string[];
  owner?: UserInfo;
}

interface ContentTabsProps {
  posts: Post[];
  loading: boolean;
}



export function ContentTabs({ posts: initialPosts = [], loading = false }: ContentTabsProps) {
  // 使用状态管理微博列表，以便可以在编辑/删除后更新
  const [posts, setPosts] = useState(initialPosts);
  const [error, setError] = useState<string | null>(null);
  // 添加评论状态管理
  const [commentVisibility, setCommentVisibility] = useState<Record<string, boolean>>({});
  const [commentInputs, setCommentInputs] = useState<Record<string, string>>({});
  // 修改获取用户信息的方式，从 localStorage 中获取
  const [user, setUser] = useState<{ id: string, email: string, username: string } | null>(null);

  // 对话框状态
  const [dialogOpen, setDialogOpen] = useState(false);
  const [selectedPost, setSelectedPost] = useState<Post | null>(null);
  const [userInput, setUserInput] = useState("");
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  // 回到顶部按钮状态
  const [showBackToTop, setShowBackToTop] = useState(false);

  // 跟踪展开的帖子和评论
  const [expandedPosts, setExpandedPosts] = useState<Record<string, boolean>>({});

  // 跟踪复制状态
  const [copiedMessageIndex, setCopiedMessageIndex] = useState<number | null>(null);
  const [expandedComments, setExpandedComments] = useState<Record<string, boolean>>({});
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // 图片预览状态
  const [previewImage, setPreviewImage] = useState<{ src: string; alt?: string } | null>(null);
  const [showImagePreview, setShowImagePreview] = useState(false);

  // 存储每个帖子的随机点赞数
  const [likeCounts, setLikeCounts] = useState<Record<string, number>>({});

  // 处理 <think> 标签，提取内容
  const extractThinkContent = (content: string): { mainContent: string, thinkContent: string | null } => {
    // 使用正则表达式匹配 <think> 标签及其内容
    const thinkRegex = /<think>([\s\S]*?)<\/think>/g;
    const matches = content.match(thinkRegex);

    // 提取 think 内容（如果有）
    let thinkContent: string | null = null;
    if (matches && matches.length > 0) {
      // 提取第一个 <think> 标签中的内容
      thinkContent = matches[0].replace(/<think>|<\/think>/g, '').trim();
    }

    // 移除所有 <think> 标签及其内容
    const mainContent = content.replace(thinkRegex, '');

    return { mainContent, thinkContent };
  };

  // 跟踪每条消息的展开状态
  const [expandedThink, setExpandedThink] = useState<Record<number, boolean>>({});

  // LLM 模型相关
  const { selectedModel, providers, setSelectedModel: setModelInStore } = useLLMStore();

  // 添加 useEffect 从 localStorage 获取用户信息
  useEffect(() => {
    const userJson = localStorage.getItem('user');
    if (userJson) {
      try {
        const userData = JSON.parse(userJson);
        setUser(userData);
      } catch (error) {
        console.error('解析用户数据失败:', error);
      }
    }
  }, []);

  // 当 initialPosts 更新时，同步更新内部状态
  useEffect(() => {
    setPosts(initialPosts);

    // 为每个帖子生成随机点赞数
    const newLikeCounts: Record<string, number> = {};
    initialPosts.forEach(post => {
      // 生成1到999之间的随机数
      newLikeCounts[post.post.id] = Math.floor(Math.random() * 999) + 1;
    });
    setLikeCounts(newLikeCounts);
  }, [initialPosts]);

  // 更新评论输入
  const updateCommentInput = (postId: string, value: string) => {
    setCommentInputs(prev => ({
      ...prev,
      [postId]: value
    }));
  };

  // 提交评论
  const submitComment = async (postId: string) => {
    // 获取评论内容
    const commentContent = commentInputs[postId];
    if (!commentContent?.trim()) return;

    // 检查内容长度
    let truncatedContent = commentContent;
    const MAX_LENGTH = 2000;

    // 前端UI限制
    if (commentContent.length > MAX_LENGTH) {
      truncatedContent = commentContent.slice(0, MAX_LENGTH);
      toast.warning(`评论内容超出${MAX_LENGTH}字符限制，已自动截断`);
    }

    // 数据库字段长度限制 (VARCHAR(2000))
    const DB_MAX_LENGTH = 2000;
    if (truncatedContent.length > DB_MAX_LENGTH) {
      truncatedContent = truncatedContent.slice(0, DB_MAX_LENGTH);
      toast.warning(`评论内容超出数据库限制(${DB_MAX_LENGTH}字符)，已自动截断`);
      console.warn(`评论内容长度(${commentContent.length})超过数据库限制(${DB_MAX_LENGTH})，已截断`);
    }

    try {
      // 获取token
      const token = localStorage.getItem('token') || sessionStorage.getItem('token');
      if (!token) {
        throw new Error('未登录，无法发表评论');
      }

      // 构建请求体
      const requestBody = {
        content: truncatedContent, // 使用截断后的内容
        // 如果需要添加图片，可以在这里添加
        images: []
      };

      // 发送请求到API
      const response = await fetch(`${API.API_PATH}/posts/${postId}/retweet`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        // 尝试获取详细的错误信息
        try {
          const errorData = await response.json();
          console.error('评论发送失败，服务器返回:', errorData);

          // 检查是否是数据库字段长度错误
          if (errorData.detail && errorData.detail.includes('StringDataRightTruncation')) {
            toast.error("评论发送失败: 内容长度超出数据库限制");
          } else {
            toast.error(`评论发送失败: ${errorData.message || errorData.detail || '服务器错误'}`);
          }
        } catch (parseError) {
          console.error('评论发送失败，状态码:', response.status, response.statusText);
          toast.error(`评论发送失败 (${response.status}): ${response.statusText}`);
        }
        return;
      }

      // 获取响应数据
      const responseData = await response.json();
      console.log('评论发送成功，新评论ID:', responseData.comment_post_id);

      // 评论成功后清空输入
      updateCommentInput(postId, "");

      // 使用 sonner 的 toast 替代 alert
      toast.success('评论发送成功');

      // 添加刷新评论列表的逻辑
      fetchComments(postId);

    } catch (error) {
      console.error('评论发送失败:', error);

      // 提供更详细的错误信息
      if (error instanceof TypeError && error.message.includes('Failed to fetch')) {
        toast.error("评论发送失败：无法连接到服务器，请检查网络连接或服务器状态");
        console.error('可能的原因：服务器未运行、网络问题或CORS错误');
      } else if (error instanceof Error) {
        toast.error(`评论发送失败：${error.message}`);
      } else {
        toast.error('评论发送失败，请检查网络连接或登录状态');
      }
    }
  };

  // 添加状态来存储评论
  const [comments, setComments] = useState<Record<string, Post[]>>({});
  const [isLoadingComments, setIsLoadingComments] = useState<Record<string, boolean>>({});

  // 获取评论
  const fetchComments = async (postId: string) => {
    setIsLoadingComments(prev => ({ ...prev, [postId]: true }));

    try {
      // 获取token
      const token = localStorage.getItem('token') || sessionStorage.getItem('token');

      // 使用新的评论API端点
      const response = await fetch(`${API.API_PATH}/posts/${postId}/comments`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });

      if (!response.ok) {
        throw new Error('获取评论失败');
      }

      const data = await response.json();
      setComments(prev => ({ ...prev, [postId]: data }));

    } catch (error) {
      console.error('获取评论失败:', error);
    } finally {
      setIsLoadingComments(prev => ({ ...prev, [postId]: false }));
    }
  };

  // 修改切换评论显示状态函数，加载评论
  const toggleComment = (postId: string) => {
    const newVisibility = !commentVisibility[postId];
    setCommentVisibility(prev => ({
      ...prev,
      [postId]: newVisibility
    }));

    // 如果打开评论区且还没有加载评论，则加载评论
    if (newVisibility && !comments[postId]) {
      fetchComments(postId);
    }
  };

  // 处理对话框打开
  const handleDialogOpen = (post: Post) => {
    setSelectedPost(post);
    setDialogOpen(true);

    // 过滤掉链接和图片标记
    const filteredContent = post.post.content
      // 移除 Markdown 图片/链接语法
      .replace(/!\[.*?\]\(.*?\)/g, '')
      .replace(/\[.*?\]\(.*?\)/g, '')
      // 移除 URL
      .replace(/https?:\/\/[^\s]+/g, '')
      // 移除 PDF 和文档链接
      .replace(/http:\/\/127\.0\.0\.1:[0-9]+\/storage\/.*?\.pdf\??[^\s]*/g, '')
      .replace(/http:\/\/127\.0\.0\.1:[0-9]+\/storage\/.*?\.docx?\??[^\s]*/g, '')
      // 移除多余的空格和换行
      // .replace(/\s+/g, ' ')
      .trim();

    setUserInput(`请探究这条微博，内容是：${filteredContent}`);
    setChatMessages([]);
  };

  // 编辑微博状态
  const [editingPost, setEditingPost] = useState<{ id: string, content: string } | null>(null);
  const [editContent, setEditContent] = useState('');
  const [isEditing, setIsEditing] = useState(false);
  // 跟踪哪个帖子正在被编辑（用于原位置编辑）
  const [editingPostId, setEditingPostId] = useState<string | null>(null);

  // 处理微博编辑
  const handleEditPost = (postId: string, content: string) => {
    // 设置编辑状态
    setEditingPost({ id: postId, content });
    setEditContent(content);
    setEditingPostId(postId); // 设置当前正在编辑的帖子ID
  };

  // 取消编辑
  const cancelEdit = () => {
    setEditingPostId(null);
    setEditingPost(null);
    setEditContent('');
  };

  // 保存编辑
  const saveEdit = async () => {
    if (!editingPost) return;

    setIsEditing(true);
    try {
      // 获取token
      const token = localStorage.getItem('token') || sessionStorage.getItem('token');

      // 发送更新请求
      console.log('发送更新请求:', editingPost.id, editContent);

      // 尝试不同的请求格式
      // 方法1: 直接发送内容字符串
      const response = await fetch(`${API.API_PATH}/posts/${editingPost.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(editContent) // 直接发送内容字符串，不包装在对象中
      });

      console.log('更新响应状态:', response.status);
      if (response.status === 422) {
        // 如果是 422 错误，尝试获取详细错误信息
        const errorData = await response.json();
        console.error('422 错误详情:', errorData);
        throw new Error(`请求格式错误: ${JSON.stringify(errorData)}`);
      }

      if (!response.ok) {
        throw new Error(`更新失败: ${response.status}`);
      }

      // 更新成功，解析响应但不需要使用
      await response.json();

      // 更新列表中的微博内容
      setPosts(prev => prev.map(item =>
        item.post.id === editingPost.id
          ? { ...item, post: { ...item.post, content: editContent } }
          : item
      ));

      toast.success('微博已更新');
      setIsEditing(false);
      setEditingPostId(null); // 退出编辑模式
      setEditingPost(null);
    } catch (error) {
      console.error('更新微博失败:', error);
      toast.error('更新失败，请重试');
    } finally {
      setIsEditing(false);
    }
  };

  // 处理微博删除
  const handleDeletePost = (postId: string) => {
    // 显示确认对话框
    toast.custom((t: any) => (
      <div className={`${t.visible ? 'animate-enter' : 'animate-leave'} max-w-md w-full bg-white dark:bg-gray-800 shadow-lg rounded-lg pointer-events-auto flex flex-col`}>
        <div className="p-4">
          <h3 className="text-sm font-medium mb-2">确认删除</h3>
          <p className="text-sm text-gray-500 dark:text-gray-400">确定要删除这条微博吗？此操作无法撤销。</p>
        </div>
        <div className="flex justify-end gap-2 p-4 pt-0">
          <button
            className="px-3 py-1.5 text-sm bg-gray-200 dark:bg-gray-700 rounded-md"
            onClick={() => toast.dismiss(t.id)}
          >
            取消
          </button>
          <button
            className="px-3 py-1.5 text-sm bg-red-500 text-white rounded-md"
            onClick={async () => {
              try {
                // 获取token
                const token = localStorage.getItem('token') || sessionStorage.getItem('token');

                // 发送删除请求
                const response = await fetch(`${API.API_PATH}/posts/${postId}`, {
                  method: 'DELETE',
                  headers: {
                    'Authorization': `Bearer ${token}`
                  }
                });

                if (!response.ok) {
                  throw new Error(`删除失败: ${response.status}`);
                }

                // 删除成功，从列表中移除该微博
                setPosts(prev => prev.filter(item => item.post.id !== postId));

                toast.success('微博已删除');
                toast.dismiss(t.id);
              } catch (error) {
                console.error('删除微博失败:', error);
                toast.error('删除失败，请重试');
              }
            }}
          >
            删除
          </button>
        </div>
      </div>
    ), { duration: Infinity });
  };

  // 处理评论删除
  const handleDeleteComment = (commentId: string, parentPostId: string) => {
    // 显示确认对话框
    toast.custom((t: any) => (
      <div className={`${t.visible ? 'animate-enter' : 'animate-leave'} max-w-md w-full bg-white dark:bg-gray-800 shadow-lg rounded-lg pointer-events-auto flex flex-col`}>
        <div className="p-4">
          <h3 className="text-sm font-medium mb-2">确认删除</h3>
          <p className="text-sm text-gray-500 dark:text-gray-400">确定要删除这条跟帖吗？此操作无法撤销。</p>
        </div>
        <div className="flex justify-end gap-2 p-4 pt-0">
          <button
            className="px-3 py-1.5 text-sm bg-gray-200 dark:bg-gray-700 rounded-md"
            onClick={() => toast.dismiss(t.id)}
          >
            取消
          </button>
          <button
            className="px-3 py-1.5 text-sm bg-red-500 text-white rounded-md"
            onClick={async () => {
              try {
                // 获取token
                const token = localStorage.getItem('token') || sessionStorage.getItem('token');

                // 发送删除请求
                const response = await fetch(`${API.API_PATH}/posts/${commentId}`, {
                  method: 'DELETE',
                  headers: {
                    'Authorization': `Bearer ${token}`
                  }
                });

                if (!response.ok) {
                  throw new Error(`删除失败: ${response.status}`);
                }

                // 删除成功，刷新评论列表
                fetchComments(parentPostId);

                toast.success('跟帖已删除');
                toast.dismiss(t.id);
              } catch (error) {
                console.error('删除跟帖失败:', error);
                toast.error('删除失败，请重试');
              }
            }}
          >
            删除
          </button>
        </div>
      </div>
    ), { duration: Infinity });
  };

  // 处理模型选择
  const handleModelChange = (value: string) => {
    // 使用特殊分隔符 ":::" 来分割 providerId 和 modelId
    const [providerId, modelId] = value.split(':::');

    if (!providerId || !modelId) {
      console.error('Invalid model value format:', value);
      return;
    }

    // 从 providers 中找到对应的模型
    const provider = providers?.find(p => p.id === providerId);
    const model = provider?.models.find(m => m.id === modelId);

    if (model && provider) {
      // 设置选中的模型
      setModelInStore(model.id);
    }
  };

  // 处理提交对话
  const handleSubmit = async () => {
    if (!userInput.trim() || !selectedModel) return;

    // 添加用户消息到对话
    const userMessage: ChatMessage = { role: 'user', content: userInput };
    setChatMessages(prev => [...prev, userMessage]);

    setIsLoading(true);
    try {
      // 获取当前选中模型的提供商
      const provider = providers.find(p =>
        p.models.some(m => m.id === selectedModel.id)
      );

      if (!provider) {
        throw new Error('无法找到选中模型的提供商信息');
      }

      // 使用模型提供商的 credential（API密钥）
      const apiKey = provider.apiKey;

      if (!apiKey) {
        throw new Error('模型未配置API密钥');
      }

      // 构建请求体
      const requestBody = {
        model: selectedModel.id,
        messages: [
          { role: "user", content: userInput }
        ],
        temperature: 0.6,
        stream: true // 启用流式输出
      };

      // 发送请求到API
      // 使用 API.SERVICES.LLM.CHAT_COMPLETIONS 函数来构建正确的 URL
      let baseUrl = selectedModel.baseUrl || API.BASE_URL;

      // 仅对非本地模型，确保 baseUrl 使用 HTTPS
      if (baseUrl.startsWith('http:') && window.location.protocol === 'https:') {
        // 检查是否是本地模型（localhost 或 127.0.0.1）
        const isLocalModel = baseUrl.includes('localhost') || baseUrl.includes('127.0.0.1');

        if (!isLocalModel) {
          baseUrl = baseUrl.replace('http:', 'https:');
          console.log('已将 HTTP URL 转换为 HTTPS:', baseUrl);
        } else {
          console.log('检测到本地模型，保留 HTTP 协议:', baseUrl);
        }
      }

      const chatCompletionsUrl = API.SERVICES.LLM.CHAT_COMPLETIONS(baseUrl);
      console.log('使用的 API URL:', chatCompletionsUrl);

      const response = await fetch(chatCompletionsUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${apiKey}`
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        throw new Error(`请求失败: ${response.status} ${response.statusText}`);
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('无法获取响应流');
      }

      const decoder = new TextDecoder('utf-8');
      let responseText = "";
      let buffer = "";
      let done = false;

      // 创建一个临时的助手消息
      const assistantMessage: ChatMessage = { role: 'assistant', content: '' };
      setChatMessages(prev => [...prev, assistantMessage]);

      while (!done) {
        const { value, done: streamDone } = await reader.read();
        done = streamDone;

        if (value) {
          const chunk = decoder.decode(value, { stream: true });
          buffer += chunk;

          // 处理SSE格式的数据
          const lines = buffer.split('\n');
          buffer = lines.pop() || ""; // 保留最后一个不完整的行

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              const data = line.slice(5);
              // 检查是否是 [DONE] 标记
              if (data.trim() === '[DONE]') {
                console.log('收到流式响应结束标记');
                continue;
              }

              try {
                const json = JSON.parse(data);
                const content = json.choices?.[0]?.delta?.content || '';
                if (content) {
                  responseText += content;
                  // 更新最后一条消息的内容
                  setChatMessages(prev => {
                    const newMessages = [...prev];
                    newMessages[newMessages.length - 1].content = responseText;
                    return newMessages;
                  });
                }
              } catch (e) {
                console.error('解析JSON失败:', e, data);
                // 不中断流程，继续处理下一行数据
              }
            }
          }
        }
      }

      // 消息完成，设置状态触发滚动
      setMessageComplete(true);

    } catch (error) {
      console.error('对话请求失败:', error);
      // 添加错误消息
      setChatMessages(prev => [
        ...prev,
        { role: 'assistant', content: "对话请求失败，请检查网络连接或模型配置" }
      ]);

      // 错误消息也需要滚动到视图中
      setMessageComplete(true);
    } finally {
      setIsLoading(false);
      setUserInput(""); // 清空输入框
    }
  };

  // 创建一个状态来跟踪消息是否完成
  const [messageComplete, setMessageComplete] = useState(false);

  // 只在消息完成时滚动到底部，避免流式输出时的抖动
  useEffect(() => {
    if (messageComplete) {
      messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
      setMessageComplete(false); // 重置状态
    }
  }, [messageComplete]);

  // 复制对话内容到剪贴板，只复制主要内容，不包含思考过程
  const copyMessageToClipboard = async (text: string, index: number) => {
    try {
      // 如果是助手消息，提取主要内容（不包含思考过程）
      let contentToCopy = text;
      if (chatMessages[index]?.role === 'assistant') {
        const { mainContent } = extractThinkContent(text);
        contentToCopy = mainContent;
      }

      await navigator.clipboard.writeText(contentToCopy);
      setCopiedMessageIndex(index);

      // 2秒后重置复制状态
      setTimeout(() => {
        setCopiedMessageIndex(null);
      }, 2000);

      toast.success('内容已复制到剪贴板');
    } catch (err) {
      console.error('复制失败:', err);
      toast.error('复制失败，请手动复制');
    }
  };

  // 切换帖子展开/折叠状态
  const togglePostExpand = (postId: string) => {
    setExpandedPosts(prev => ({
      ...prev,
      [postId]: !prev[postId]
    }));
  };

  // 切换评论展开/折叠状态
  const toggleCommentExpand = (commentId: string) => {
    setExpandedComments(prev => ({
      ...prev,
      [commentId]: !prev[commentId]
    }));
  };

  // 监听滚动事件，控制回到顶部按钮的显示和隐藏
  useEffect(() => {
    const handleScroll = () => {
      // 当滚动位置超过300px时显示按钮，否则隐藏
      if (window.scrollY > 300) {
        setShowBackToTop(true);
      } else {
        setShowBackToTop(false);
      }
    };

    // 添加滚动事件监听
    window.addEventListener('scroll', handleScroll);

    // 组件卸载时移除事件监听
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  // 回到顶部的函数
  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  // 处理图片点击，打开预览
  const handleImageClick = (src: string, alt?: string) => {
    // 确保在预览时也应用URL转换
    const convertedSrc = convertSupabaseUrl(src);
    setPreviewImage({ src: convertedSrc, alt });
    setShowImagePreview(true);
  };


  if (loading) {
    return <div className="flex justify-center items-center py-8">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500"></div>
    </div>;
  }

  if (error) {
    return <div className="text-center py-8 text-red-500">{error}</div>;
  }

  if (posts.length === 0) {
    return <div className="text-center py-8 text-muted-foreground">暂无微博内容</div>;
  }

  return (
    <>
      {/* 回到顶部按钮 */}
      {showBackToTop && (
        <button
          onClick={scrollToTop}
          className="fixed bottom-6 right-6 p-3 bg-orange-500 hover:bg-orange-600 text-white rounded-full shadow-lg transition-all duration-300 z-50"
          aria-label="回到顶部"
        >
          <ArrowUp className="h-5 w-5" />
        </button>
      )}

      <div className="space-y-4">
        {posts.map((postItem, index) => (
          <Card key={postItem.post.id || index} id={`post-${postItem.post.id}`}>
            {/* 卡片内容保持不变 */}
            <CardHeader className="p-4 pb-0 flex flex-row items-start gap-3">
              <Avatar className="h-10 w-10 border-2 border-orange-500">
                <AvatarImage src={postItem.owner?.thumbnail || "/placeholder.svg?height=40&width=40"} alt="@user" />
                <AvatarFallback>
                  {postItem.owner?.display_name?.slice(0, 2) ||
                    postItem.owner?.username?.slice(0, 2) ||
                    postItem.post.owner_id.slice(0, 2)}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1">
                <div className="flex items-center gap-1">
                  <span className="font-bold">
                    {postItem.owner?.display_name ||
                      postItem.owner?.username ||
                      (postItem.post.owner_id.includes('@')
                        ? postItem.post.owner_id.split('@')[0]
                        : postItem.post.owner_id.slice(0, 8))}
                  </span>
                  <Badge className="bg-yellow-500 text-xs">V</Badge>
                  <div className="flex items-center gap-2 ml-auto">

                    {/* 编辑和删除按钮 - 仅对用户自己的帖子显示 */}
                    {user && user.id === postItem.post.owner_id && (
                      <>
                        <button
                          className="text-gray-400 hover:text-blue-500 transition-colors p-1 rounded-full"
                          onClick={(e) => {
                            e.preventDefault(); // 阻止默认行为
                            e.stopPropagation(); // 阻止事件冒泡
                            handleEditPost(postItem.post.id, postItem.post.content);
                            return false; // 确保不会触发其他事件
                          }}
                          title="编辑微博"
                          type="button"
                        >
                          <Pencil className="h-3.5 w-3.5" />
                        </button>
                        <button
                          className="text-gray-400 hover:text-red-500 transition-colors p-1 rounded-full"
                          onClick={(e) => {
                            e.preventDefault(); // 阻止默认行为
                            e.stopPropagation(); // 阻止事件冒泡
                            handleDeletePost(postItem.post.id);
                            return false; // 确保不会触发其他事件
                          }}
                          title="删除微博"
                          type="button"
                        >
                          <Trash2 className="h-3.5 w-3.5" />
                        </button>
                      </>
                    )}
                  </div>
                </div>
                <div className="text-xs text-muted-foreground">
                  {new Date(postItem.post.timestamp).toLocaleString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit'
                  })}</div>
              </div>
            </CardHeader>
            <CardContent className="p-4">
              <div className="space-y-4">
                {/* 如果当前帖子正在编辑中，显示编辑界面 */}
                {editingPostId === postItem.post.id ? (
                  <div className="space-y-3">
                    <Textarea
                      value={editContent}
                      onChange={(e) => setEditContent(e.target.value)}
                      className="w-full min-h-[100px] p-2 border rounded-md"
                      placeholder="编辑微博内容..."
                    />
                    <div className="flex justify-end gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={cancelEdit}
                      >
                        取消
                      </Button>
                      <Button
                        size="sm"
                        onClick={saveEdit}
                        disabled={isEditing || !editContent.trim()}
                      >
                        {isEditing ? (
                          <span className="flex items-center gap-1">
                            <span className="h-3 w-3 border-2 border-white border-t-transparent rounded-full animate-spin"></span>
                            保存中
                          </span>
                        ) : '保存'}
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div className="markdown-content">
                    {/* 判断内容长度，超过300个字符时折叠显示 */}
                    {postItem.post.content.length > 300 && !expandedPosts[postItem.post.id] ? (
                      <div>
                        <div className="relative">
                          <ReactMarkdown
                            remarkPlugins={[remarkGfm, remarkMath]}
                            rehypePlugins={[rehypeKatex]}
                            components={{
                              // 使用我们的 MarkdownComponents 作为基础
                              ...MarkdownComponents,
                              // 自定义图片渲染为链接（覆盖 MarkdownComponents 中的 img 组件）
                              img: ({ src, alt }: { src?: string, alt?: string }) => {
                                // 检查src是否为PDF或其他文档链接
                                const isDocument = src && (
                                  src.endsWith('.pdf') ||
                                  src.endsWith('.doc') ||
                                  src.endsWith('.docx') ||
                                  src.includes('documents')
                                );

                                if (isDocument) {
                                  // 转换 URL 以适应 Docker 环境
                                  const convertedDocSrc = src ? convertUrlForDocker(src) : src;
                                  return (
                                    <span className="inline-block mt-2">
                                      <a
                                        href={convertedDocSrc}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        className="inline-flex items-center px-3 py-1 bg-orange-100 hover:bg-orange-200 rounded-md text-sm"
                                      >
                                        <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                        </svg>
                                        查看附件
                                      </a>
                                    </span>
                                  );
                                }

                                // 如果是普通图片，正常渲染但保持一致的样式
                                // 转换 Supabase 存储 URL
                                const convertedSrc = src ? convertSupabaseUrl(src) : src;

                                return (
                                  <div className="relative w-full flex justify-center my-2">
                                    <img
                                      src={convertedSrc}
                                      alt={alt || '图片'}
                                      className="max-w-[85%] rounded-md object-contain"
                                      style={{ maxHeight: '300px' }}
                                    />
                                  </div>
                                );
                              }
                            }}
                          >
                            {postItem.post.content.substring(0, 300) + '...'}
                          </ReactMarkdown>
                          <button
                            onClick={() => togglePostExpand(postItem.post.id)}
                            className="text-blue-500 hover:text-blue-700 text-sm inline-flex items-center float-right ml-2"
                          >
                            <ChevronDown className="h-4 w-4 mr-1" />
                            展开全文
                          </button>
                          <div className="clear-both"></div>
                        </div>
                      </div>
                    ) : (
                      <div>
                        <ReactMarkdown
                          remarkPlugins={[remarkGfm, remarkMath]}
                          rehypePlugins={[rehypeKatex]}
                          components={{
                            // 使用我们的 MarkdownComponents 作为基础
                            ...MarkdownComponents,
                            // 自定义图片渲染为链接（覆盖 MarkdownComponents 中的 img 组件）
                            img: ({ src, alt }: { src?: string, alt?: string }) => {
                              // 检查src是否为PDF或其他文档链接
                              const isDocument = src && (
                                src.endsWith('.pdf') ||
                                src.endsWith('.doc') ||
                                src.endsWith('.docx') ||
                                src.includes('documents')
                              );

                              if (isDocument) {
                                // 转换 URL 以适应 Docker 环境
                                const convertedDocSrc = src ? convertUrlForDocker(src) : src;
                                return (
                                  <span className="inline-block mt-2">
                                    <a
                                      href={convertedDocSrc}
                                      target="_blank"
                                      rel="noopener noreferrer"
                                      className="inline-flex items-center px-3 py-1 bg-orange-100 hover:bg-orange-200 rounded-md text-sm"
                                    >
                                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                      </svg>
                                      查看附件
                                    </a>
                                  </span>
                                );
                              }

                              // 如果是普通图片，添加点击预览功能
                              // 转换 Supabase 存储 URL
                              const convertedSrc = src ? convertSupabaseUrl(src) : src;

                              return (
                                <div className="relative w-full flex justify-center my-2 group">
                                  <img
                                    src={convertedSrc}
                                    alt={alt || '图片'}
                                    className="max-w-[85%] rounded-md cursor-pointer hover:opacity-95 object-contain"
                                    style={{ maxHeight: '500px' }}
                                    onClick={() => convertedSrc && handleImageClick(convertedSrc, alt || '图片')}
                                  />
                                  <div className="absolute top-2 right-[10%] opacity-0 group-hover:opacity-100 transition-opacity">
                                    <button
                                      className="bg-black/50 text-white p-1 rounded-full hover:bg-black/70"
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        convertedSrc && handleImageClick(convertedSrc, alt || '图片');
                                      }}
                                      title="查看大图"
                                    >
                                      <ZoomIn className="h-4 w-4" />
                                    </button>
                                  </div>
                                </div>
                              );
                            }
                          }}
                        >
                          {postItem.post.content}
                        </ReactMarkdown>
                        {postItem.post.content.length > 300 && (
                          <div className="text-right">
                            <button
                              onClick={() => togglePostExpand(postItem.post.id)}
                              className="text-blue-500 hover:text-blue-700 text-sm inline-flex items-center"
                            >
                              <ChevronUp className="h-4 w-4 mr-1" />
                              收起全文
                            </button>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                )}

                {postItem.images && postItem.images.length > 0 && (
                  <div className="relative w-full flex justify-center my-3 group">
                    {/* 转换 Supabase 存储 URL */}
                    {(() => {
                      const convertedImageSrc = convertSupabaseUrl(postItem.images[0]) || "/placeholder.svg?height=400&width=600";
                      return (
                        <>
                          <img
                            src={convertedImageSrc}
                            alt="Post image"
                            className="max-w-[85%] rounded-md cursor-pointer hover:opacity-95 object-contain"
                            style={{ maxHeight: '500px' }}
                            onClick={() => handleImageClick(convertedImageSrc, 'Post image')}
                          />
                          <div className="absolute top-2 right-[10%] opacity-0 group-hover:opacity-100 transition-opacity">
                            <button
                              className="bg-black/50 text-white p-1.5 rounded-full hover:bg-black/70"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleImageClick(convertedImageSrc, 'Post image');
                              }}
                              title="查看大图"
                            >
                              <ZoomIn className="h-5 w-5" />
                            </button>
                          </div>
                        </>
                      );
                    })()}
                  </div>
                )}
              </div>
            </CardContent>
            <CardFooter className="p-0 border-t border-gray-100">
              <div className="grid grid-cols-3 w-full">
                <Button
                  variant="ghost"
                  className="rounded-none py-1 h-8 text-muted-foreground text-xs"
                  onClick={() => handleDialogOpen(postItem)}
                >
                  <BotMessageSquare className="h-3.5 w-3.5 mr-1.5" />对话
                </Button>
                <Button
                  variant="ghost"
                  className="rounded-none py-1 h-8 text-muted-foreground text-xs"
                  onClick={() => toggleComment(postItem.post.id)}
                >
                  <MessageSquare className="h-3.5 w-3.5 mr-1.5" />评论 {comments[postItem.post.id] ? `${comments[postItem.post.id].length}` : ''}
                </Button>
                <Button
                  variant="ghost"
                  className="rounded-none py-1 h-8 text-muted-foreground text-xs"
                >
                  <ThumbsUp className="h-3.5 w-3.5 mr-1.5" />{likeCounts[postItem.post.id] || 0}
                </Button>
              </div>
            </CardFooter>

            {/* 评论区域 */}
            {commentVisibility[postItem.post.id] && (
              <div className="p-3 border-t bg-gray-50">
                {/* 显示已有评论 */}
                {comments[postItem.post.id] && comments[postItem.post.id].length > 0 ? (
                  <div className="space-y-3 mb-3">
                    {comments[postItem.post.id].map((comment, idx) => (
                      <div key={idx} className="group flex items-start gap-2 p-2 rounded-lg bg-gray-100 border border-gray-200 hover:bg-gray-50 transition-colors">
                        <Avatar className="h-6 w-6">
                          <AvatarImage src={comment.owner?.thumbnail || "/placeholder.svg?height=32&width=32"} alt="@user" />
                          <AvatarFallback>
                            {comment.owner?.display_name?.slice(0, 2) ||
                              comment.owner?.username?.slice(0, 2) ||
                              comment.post.owner_id.slice(0, 2)}
                          </AvatarFallback>
                        </Avatar>
                        <div className="flex-1">
                          <div className="flex items-center gap-1">
                            <span className="text-xs font-medium text-gray-800">
                              {comment.post.owner_id === user?.id
                                ? `${user.username || '我'}`
                                : (comment.owner?.display_name ||
                                  comment.owner?.username ||
                                  (comment.post.owner_id.includes('@')
                                    ? comment.post.owner_id.split('@')[0]
                                    : comment.post.owner_id.slice(0, 8)))}
                            </span>
                            <span className="text-xs text-gray-500 ml-auto">
                              {new Date(comment.post.timestamp).toLocaleString('zh-CN', {
                                month: '2-digit',
                                day: '2-digit',
                                hour: '2-digit',
                                minute: '2-digit'
                              })}
                            </span>
                            {/* 只有评论作者才能看到删除按钮 */}
                            {comment.post.owner_id === user?.id && (
                              <button
                                onClick={() => handleDeleteComment(comment.post.id, postItem.post.id)}
                                className="text-xs text-red-500 hover:text-red-700 ml-2 opacity-0 group-hover:opacity-100 transition-opacity"
                                title="删除跟帖"
                              >
                                <Trash2 className="h-3 w-3" />
                              </button>
                            )}
                          </div>
                          <div className="text-xs mt-1 markdown-content text-gray-700">
                            {/* 判断评论内容长度，超过150个字符时折叠显示 */}
                            {comment.post.content.length > 150 && !expandedComments[comment.post.id] ? (
                              <div>
                                <ReactMarkdown
                                  remarkPlugins={[remarkGfm, remarkMath]}
                                  rehypePlugins={[rehypeKatex]}
                                  components={{
                                    // 使用我们的 MarkdownComponents 作为基础
                                    ...MarkdownComponents,
                                    // 自定义图片渲染为链接（覆盖 MarkdownComponents 中的 img 组件）
                                    img: ({ src, alt }: { src?: string, alt?: string }) => {
                                      // 检查src是否为PDF或其他文档链接
                                      const isDocument = src && (
                                        src.endsWith('.pdf') ||
                                        src.endsWith('.doc') ||
                                        src.endsWith('.docx') ||
                                        src.includes('documents')
                                      );

                                      if (isDocument) {
                                        // 转换 URL 以适应 Docker 环境
                                        const convertedDocSrc = src ? convertUrlForDocker(src) : src;
                                        return (
                                          <span className="inline-block mt-2">
                                            <a
                                              href={convertedDocSrc}
                                              target="_blank"
                                              rel="noopener noreferrer"
                                              className="inline-flex items-center px-3 py-1 bg-orange-100 hover:bg-orange-200 rounded-md text-sm"
                                            >
                                              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                              </svg>
                                              查看附件
                                            </a>
                                          </span>
                                        );
                                      }

                                      // 如果是普通图片，添加点击预览功能
                                      // 转换 Supabase 存储 URL
                                      const convertedSrc = src ? convertSupabaseUrl(src) : src;

                                      return (
                                        <div className="relative w-full flex justify-center my-2 group">
                                          <img
                                            src={convertedSrc}
                                            alt={alt || '图片'}
                                            className="max-w-[85%] rounded-md cursor-pointer hover:opacity-95 object-contain"
                                            style={{ maxHeight: '500px' }}
                                            onClick={() => convertedSrc && handleImageClick(convertedSrc, alt || '图片')}
                                          />
                                          <div className="absolute top-2 right-[10%] opacity-0 group-hover:opacity-100 transition-opacity">
                                            <button
                                              className="bg-black/50 text-white p-1 rounded-full hover:bg-black/70"
                                              onClick={(e) => {
                                                e.stopPropagation();
                                                convertedSrc && handleImageClick(convertedSrc, alt || '图片');
                                              }}
                                              title="查看大图"
                                            >
                                              <ZoomIn className="h-4 w-4" />
                                            </button>
                                          </div>
                                        </div>
                                      );
                                    }
                                  }}
                                >
                                  {comment.post.content.substring(0, 150) + '...'}
                                </ReactMarkdown>
                                <div className="text-right">
                                  <button
                                    onClick={() => toggleCommentExpand(comment.post.id)}
                                    className="text-orange-500 hover:text-orange-700 text-xs inline-flex items-center"
                                  >
                                    <ChevronDown className="h-3 w-3 mr-1" />
                                    展开
                                  </button>
                                </div>
                              </div>
                            ) : (
                              <div>
                                <ReactMarkdown
                                  remarkPlugins={[remarkGfm, remarkMath]}
                                  rehypePlugins={[rehypeKatex]}
                                  components={{
                                    // 使用我们的 MarkdownComponents 作为基础
                                    ...MarkdownComponents,
                                    // 自定义图片渲染为链接（覆盖 MarkdownComponents 中的 img 组件）
                                    img: ({ src, alt }: { src?: string, alt?: string }) => {
                                      // 检查src是否为PDF或其他文档链接
                                      const isDocument = src && (
                                        src.endsWith('.pdf') ||
                                        src.endsWith('.doc') ||
                                        src.endsWith('.docx') ||
                                        src.includes('documents')
                                      );

                                      if (isDocument) {
                                        // 转换 URL 以适应 Docker 环境
                                        const convertedDocSrc = src ? convertUrlForDocker(src) : src;
                                        return (
                                          <span className="inline-block mt-2">
                                            <a
                                              href={convertedDocSrc}
                                              target="_blank"
                                              rel="noopener noreferrer"
                                              className="inline-flex items-center px-3 py-1 bg-orange-100 hover:bg-orange-200 rounded-md text-sm"
                                            >
                                              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                              </svg>
                                              查看附件
                                            </a>
                                          </span>
                                        );
                                      }

                                      // 如果是普通图片，添加点击预览功能
                                      // 转换 Supabase 存储 URL
                                      const convertedSrc = src ? convertSupabaseUrl(src) : src;

                                      return (
                                        <div className="relative w-full flex justify-center my-2 group">
                                          <img
                                            src={convertedSrc}
                                            alt={alt || '图片'}
                                            className="max-w-[85%] rounded-md cursor-pointer hover:opacity-95 object-contain"
                                            style={{ maxHeight: '500px' }}
                                            onClick={() => convertedSrc && handleImageClick(convertedSrc, alt || '图片')}
                                          />
                                          <div className="absolute top-2 right-[10%] opacity-0 group-hover:opacity-100 transition-opacity">
                                            <button
                                              className="bg-black/50 text-white p-1 rounded-full hover:bg-black/70"
                                              onClick={(e) => {
                                                e.stopPropagation();
                                                convertedSrc && handleImageClick(convertedSrc, alt || '图片');
                                              }}
                                              title="查看大图"
                                            >
                                              <ZoomIn className="h-4 w-4" />
                                            </button>
                                          </div>
                                        </div>
                                      );
                                    }
                                  }}
                                >
                                  {comment.post.content}
                                </ReactMarkdown>
                                {comment.post.content.length > 150 && (
                                  <div className="text-right">
                                    <button
                                      onClick={() => toggleCommentExpand(comment.post.id)}
                                      className="text-orange-500 hover:text-orange-700 text-xs inline-flex items-center"
                                    >
                                      <ChevronUp className="h-3 w-3 mr-1" />
                                      收起
                                    </button>
                                  </div>
                                )}
                              </div>
                            )}
                          </div>
                          {comment.images && comment.images.length > 0 && (
                            <div className="mt-2 rounded-lg overflow-hidden relative group flex justify-center">
                              {/* 转换 Supabase 存储 URL */}
                              {(() => {
                                const convertedImageSrc = convertSupabaseUrl(comment.images[0]);
                                return (
                                  <>
                                    <img
                                      src={convertedImageSrc}
                                      alt="Comment image"
                                      className="max-w-[85%] max-h-[200px] object-contain cursor-pointer hover:opacity-95 rounded-md"
                                      onClick={() => handleImageClick(convertedImageSrc, 'Comment image')}
                                    />
                                    <div className="absolute top-2 right-[10%] opacity-0 group-hover:opacity-100 transition-opacity">
                                      <button
                                        className="bg-black/50 text-white p-1 rounded-full hover:bg-black/70"
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          handleImageClick(convertedImageSrc, 'Comment image');
                                        }}
                                        title="查看大图"
                                      >
                                        <ZoomIn className="h-4 w-4" />
                                      </button>
                                    </div>
                                  </>
                                );
                              })()}
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : isLoadingComments[postItem.post.id] ? (
                  <div className="flex justify-center items-center py-4">
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-orange-500"></div>
                  </div>
                ) : (
                  <div className="text-center py-2 text-sm text-gray-500">暂无评论</div>
                )}

                {/* 评论输入框 */}
                <div className="flex items-start gap-2 mt-3 p-2 bg-gray-100 rounded-lg border border-gray-200">
                  <Avatar className="h-6 w-6">
                    <AvatarImage src="/placeholder.svg?height=32&width=32" alt="@user" />
                    <AvatarFallback>U</AvatarFallback>
                  </Avatar>
                  <div className="flex-1">
                    <div className="flex flex-col space-y-1.5 w-full">
                      <div className="relative">
                        <Textarea
                          placeholder="发布你的评论"
                          value={commentInputs[postItem.post.id] || ""}
                          onChange={(e) => updateCommentInput(postItem.post.id, e.target.value)}
                          className="min-h-[40px] text-xs resize-none bg-white border-gray-200 focus:border-orange-400 focus:ring-orange-400 py-1.5 px-2"
                          maxLength={2000} // 添加最大长度限制
                        />
                        <div className="text-xs text-right mt-1">
                          <span
                            className={`
                              ${(commentInputs[postItem.post.id]?.length || 0) > 2000 ? 'text-red-500 font-medium' :
                                (commentInputs[postItem.post.id]?.length || 0) > 2000 * 0.8 ? 'text-yellow-500' :
                                'text-green-500'}
                            `}
                          >
                            {commentInputs[postItem.post.id]?.length || 0}/2000
                          </span>
                        </div>
                      </div>
                      <div className="flex justify-between items-center">
                        <div className="flex items-center gap-1.5">
                          <button className="text-gray-500 hover:text-orange-500">
                            <SmilePlus className="h-4 w-4" />
                          </button>
                          <button className="text-gray-500 hover:text-orange-500">
                            <Image className="h-4 w-4" />
                          </button>
                        </div>
                        <Button
                          size="sm"
                          onClick={() => submitComment(postItem.post.id)}
                          disabled={!commentInputs[postItem.post.id]?.trim()}
                          className="bg-orange-500 hover:bg-orange-600 text-white h-7 text-xs px-2"
                        >
                          跟帖
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </Card>
        ))}
      </div>

      {/* 图片预览 */}
      {previewImage && (
        <ImagePreview
          src={previewImage.src}
          alt={previewImage.alt}
          open={showImagePreview}
          onOpenChange={setShowImagePreview}
        />
      )}

      {/* 对话框 */}
      <div
        className={`fixed right-4 top-[10vh] w-[450px] h-[80vh] bg-white dark:bg-gray-900 shadow-xl rounded-lg flex flex-col overflow-hidden transition-all duration-300 ease-in-out z-50 ${dialogOpen ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-full pointer-events-none'
          }`}
        style={{ maxHeight: '80vh', height: '80vh' }} // 确保高度固定
      >
        {/* 对话框标题栏 - 更小的高度 */}
        <div className="flex items-center justify-between py-2.5 px-4 border-b bg-gradient-to-r from-orange-500 to-amber-500 text-white">
          <div className="text-base font-bold flex items-center gap-2">
            <BotMessageSquare className="h-5 w-5" />
            探究这条微博
          </div>
          <button
            onClick={() => setDialogOpen(false)}
            className="p-1 rounded-full hover:bg-white/20"
          >
            <X className="h-4 w-4" />
          </button>
        </div>

        {/* 对话消息区域 - 占据主要部分，可滚动，添加底部内边距避免被footer遮挡 */}
        <div className="flex-1 overflow-y-auto px-4 pt-4 pb-20 space-y-4" style={{ height: 'calc(100% - 50px)' }}>
          {chatMessages.length === 0 ? (
            <div className="h-full flex items-center justify-center text-gray-400">
              <p className="text-center">开始与AI助手对话，探究微博内容</p>
            </div>
          ) : (
            chatMessages.map((message, index) => (
              <div key={index} className="flex flex-col mb-5">
                {/* 消息头部 - 显示角色和模型名称 */}
                <div className="mb-2 text-xs font-medium flex items-center">
                  {message.role === 'assistant' ? (
                    <div className="flex items-center gap-1.5 text-blue-600 dark:text-blue-400">
                      <BotMessageSquare className="h-3.5 w-3.5" />
                      {/* <span>AI助手</span> */}
                      {selectedModel && (
                        // <span className="text-xs text-gray-500 dark:text-gray-400 ml-1">
                        selectedModel.id
                        // </span>
                      )}
                    </div>
                  ) : (
                    <div className="flex items-center gap-1.5 text-orange-600 dark:text-orange-400">
                      <span>您</span>
                    </div>
                  )}
                </div>

                {/* 消息内容 - 所有消息都靠左对齐 */}
                <div
                  className={`w-full rounded-lg p-4 relative ${message.role === 'assistant'
                    ? 'bg-blue-50 dark:bg-blue-900/10 border border-blue-100 dark:border-blue-900/30'
                    : 'bg-orange-50 dark:bg-orange-900/10 border border-orange-100 dark:border-orange-900/30'
                    }`}
                >

                  {message.role === 'assistant' ? (
                    <div className="prose prose-sm dark:prose-invert max-w-none">
                      {/* 提取并处理 think 内容 */}
                      {(() => {
                        const { mainContent, thinkContent } = extractThinkContent(message.content);
                        return (
                          <>
                            {/* 主要内容 */}
                            <ReactMarkdown
                              remarkPlugins={[remarkGfm, remarkMath]}
                              rehypePlugins={[rehypeKatex]}
                              components={MarkdownComponents}
                            >
                              {mainContent}
                            </ReactMarkdown>

                            {/* 底部操作栏 - 包含复制按钮和思考过程按钮 */}
                            <div className="mt-3 border-t border-gray-200 dark:border-gray-700 pt-2 flex justify-between items-center">
                              {/* 复制按钮 */}
                              <button
                                className="flex items-center gap-1 text-xs text-gray-500 hover:text-blue-500 transition-colors"
                                onClick={(e) => {
                                  e.preventDefault();
                                  e.stopPropagation();
                                  copyMessageToClipboard(message.content, index);
                                }}
                                title="复制内容"
                                type="button"
                              >
                                {copiedMessageIndex === index ? (
                                  <><Check className="h-3 w-3 text-green-500" /><span>已复制</span></>
                                ) : (
                                  <><Copy className="h-3 w-3" /><span>复制</span></>
                                )}
                              </button>

                              {/* 思考过程按钮 - 仅当有思考内容时显示 */}
                              {thinkContent && (
                                <button
                                  onClick={() => setExpandedThink(prev => ({
                                    ...prev,
                                    [index]: !prev[index]
                                  }))}
                                  className="flex items-center gap-1 text-xs text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
                                >
                                  {expandedThink[index] ? (
                                    <ChevronDown className="h-3 w-3" />
                                  ) : (
                                    <ChevronRight className="h-3 w-3" />
                                  )}
                                  <span>思考过程</span>
                                </button>
                              )}

                            </div>

                            {/* 展开的思考内容 */}
                            {thinkContent && expandedThink[index] && (
                              <div className="mt-2 p-2 bg-gray-100 dark:bg-gray-800 rounded text-xs text-gray-600 dark:text-gray-400">
                                <ReactMarkdown
                                  remarkPlugins={[remarkGfm, remarkMath]}
                                  rehypePlugins={[rehypeKatex]}
                                  components={MarkdownComponents}
                                >
                                  {thinkContent}
                                </ReactMarkdown>
                              </div>
                            )}
                          </>
                        );
                      })()}
                    </div>
                  ) : (
                    <div>
                      <p className="text-sm">{message.content}</p>

                      {/* 用户消息的复制按钮 */}
                      <div className="mt-3 border-t border-gray-200 dark:border-gray-700 pt-2 flex justify-end items-center">
                        <button
                          className="flex items-center gap-1 text-xs text-gray-500 hover:text-blue-500 transition-colors"
                          onClick={(e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            copyMessageToClipboard(message.content, index);
                          }}
                          title="复制内容"
                          type="button"
                        >
                          {copiedMessageIndex === index ? (
                            <><Check className="h-3 w-3 text-green-500" /><span>已复制</span></>
                          ) : (
                            <><Copy className="h-3 w-3" /><span>复制</span></>
                          )}
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            ))
          )}
          <div ref={messagesEndRef} />
        </div>

        {/* 输入区域 - 固定在底部的footer，单行设计 */}
        <div className="absolute bottom-0 left-0 right-0 p-3 border-t bg-gray-50 dark:bg-gray-800/50">
          <div className="flex items-center gap-2">
            {/* 模型选择按钮 */}
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  size="icon"
                  className="h-10 w-10 rounded-sm bg-white dark:bg-gray-700 border-gray-200"
                  title={selectedModel ? `当前模型: ${selectedModel.id}` : "选择模型"}
                >
                  <Brain className={`h-5 w-5 ${selectedModel ? 'text-orange-500' : 'text-gray-400'}`} />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-[220px] p-0" align="start">
                <div className="p-2 font-medium text-sm border-b">选择模型</div>
                <div className="max-h-[300px] overflow-y-auto py-1">
                  {providers?.length > 0 ? (
                    providers.map(provider => (
                      <div key={provider.id} className="px-1">
                        <div className="px-2 py-1 text-xs font-semibold text-gray-500 bg-gray-50">
                          {provider.name || provider.id}
                        </div>
                        {provider.models.map(model => (
                          <button
                            key={`${provider.id}-${model.id}`}
                            className={`w-full text-left px-2 py-1.5 text-sm hover:bg-gray-100 dark:hover:bg-gray-700 rounded my-0.5 flex items-center gap-2 ${selectedModel?.id === model.id ? 'bg-orange-100 dark:bg-orange-900/30 font-medium' : ''
                              }`}
                            onClick={() => handleModelChange(`${provider.id}:::${model.id}`)}
                          >
                            <div className={`w-2 h-2 rounded-full ${provider.color || 'bg-gray-400'}`}></div>
                            {model.name}
                          </button>
                        ))}
                      </div>
                    ))
                  ) : (
                    <div className="px-2 py-2 text-sm text-gray-500">暂无可用模型</div>
                  )}
                </div>
              </PopoverContent>
            </Popover>

            {/* 单行输入框 */}
            <div className="flex-1 relative">
              <Textarea
                value={userInput}
                onChange={(e) => setUserInput(e.target.value)}
                placeholder="输入您的问题... (Alt+Enter 快速提交)"
                className="min-h-[40px] max-h-[120px] resize-none pr-10 py-2 bg-white dark:bg-gray-700"
                onKeyDown={(e) => {
                  // 添加 Alt+Enter 快捷键支持
                  if (e.altKey && e.key === 'Enter') {
                    e.preventDefault();
                    if (!isLoading && userInput.trim() && selectedModel) {
                      handleSubmit();
                    }
                  }
                }}
              />
              <Button
                onClick={handleSubmit}
                disabled={isLoading || !userInput.trim() || !selectedModel}
                className="absolute right-2 bottom-2 h-8 w-8 p-0 rounded-full"
                variant="ghost"
              >
                {isLoading ? (
                  <span className="h-4 w-4 border-2 border-orange-500 border-t-transparent rounded-full animate-spin"></span>
                ) : (
                  <Send className="h-4 w-4 text-orange-500" />
                )}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}
