/**
 * URL 工具函数
 * 用于处理各种 URL 转换和格式化
 */

/**
 * 获取当前主机地址
 * 在局域网环境中，使用当前浏览器的主机地址
 */
function getCurrentHost(): string {
  if (typeof window !== 'undefined') {
    return window.location.host;
  }
  return 'localhost:8080'; // 默认值
}

/**
 * 将 Supabase 存储 URL 转换为浏览器可访问的 URL
 * 支持局域网访问，使用当前主机地址而不是硬编码的localhost
 *
 * @param url 原始 URL
 * @returns 转换后的 URL
 */
export function convertSupabaseUrl(url: string): string {
  if (!url) return url;

  // 检查是否是 Supabase 存储 URL
  if (url.includes('host.docker.internal:54321')) {
    // 在Docker环境中，通过Nginx代理访问存储
    const currentHost = getCurrentHost();
    return url.replace('http://host.docker.internal:54321/storage', `http://${currentHost}/storage-proxy`);
  }

  // 检查是否是 127.0.0.1:54321
  if (url.includes('127.0.0.1:54321')) {
    // 在Docker环境中，通过Nginx代理访问存储
    const currentHost = getCurrentHost();
    return url.replace('http://127.0.0.1:54321/storage', `http://${currentHost}/storage-proxy`);
  }

  // 检查是否是 localhost:54321
  if (url.includes('localhost:54321')) {
    // 在Docker环境中，通过Nginx代理访问存储
    const currentHost = getCurrentHost();
    return url.replace('http://localhost:54321/storage', `http://${currentHost}/storage-proxy`);
  }

  return url;
}

/**
 * 转换 URL 以适应浏览器环境
 * 支持局域网访问，使用当前主机地址而不是硬编码的localhost
 *
 * @param url 原始 URL
 * @returns 转换后的 URL
 */
export function convertUrlForDocker(url: string): string {
  if (!url) return url;

  // 检查是否包含 host.docker.internal
  if (url.includes('host.docker.internal:')) {
    const currentHost = getCurrentHost();
    // 如果是存储URL，使用代理路径
    if (url.includes('host.docker.internal:54321/storage')) {
      return url.replace('http://host.docker.internal:54321/storage', `http://${currentHost}/storage-proxy`);
    }
    // 其他情况，替换为当前主机
    return url.replace('host.docker.internal:', `${window.location.hostname}:`);
  }

  // 检查是否包含 127.0.0.1 或 localhost，也需要处理
  if (url.includes('127.0.0.1:54321/storage') || url.includes('localhost:54321/storage')) {
    const currentHost = getCurrentHost();
    return url
      .replace('http://127.0.0.1:54321/storage', `http://${currentHost}/storage-proxy`)
      .replace('http://localhost:54321/storage', `http://${currentHost}/storage-proxy`);
  }

  return url;
}

/**
 * 格式化 API URL
 * 确保 URL 格式正确，避免重复的斜杠等问题
 *
 * @param baseUrl 基础 URL
 * @param path 路径
 * @returns 格式化后的 URL
 */
export function formatApiUrl(baseUrl: string, path: string): string {
  // 移除 baseUrl 末尾的斜杠
  const cleanBaseUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;

  // 确保 path 以斜杠开头
  const cleanPath = path.startsWith('/') ? path : `/${path}`;

  return `${cleanBaseUrl}${cleanPath}`;
}
