# 3Stooges Portal

基于3Stooges的学术研究门户，一个全新的基于思维链和Memory整合的学术追问和反思平台。
- 尝试一人开发、测试与运维

## 目录

1. [功能特点](#功能特点)
2. [系统架构](#系统架构)
3. [安装部署指南](#安装部署指南)
   - [系统要求](#系统要求)
   - [安装步骤](#安装步骤)
4. [各组件说明](#各组件说明)
   - [后端服务 (Backend)](#后端服务-backend)
   - [前端应用 (Frontend)](#前端应用-frontend)
   - [PDF服务 (PDF Service)](#pdf服务-pdf-service)
   - [Supabase 服务](#supabase-服务)
5. [生产环境部署](#生产环境部署)
6. [常见问题解决](#常见问题解决)
7. [功能列表](#功能列表)

## 功能特点

- 🔍 Supabase作为数据提供和认证服务
- 💡 Vite作为前端App管理
- 🤖 文档服务：PDF_Service 支持文档解析和转换
- 📊 学术图表生成与管理（支持Mermaid和PlantUML）
- 📝 学术微博与交流
- 📚 知识库管理与检索
- 🧠 AI模型集成与管理
- 🔎 多种搜索引擎集成（Tavily、Bing、Exa等）
- 📑 论文库管理与分析
- 💭 思维链可视化

## 系统架构

整个系统由四个主要组件构成：

1. **后端服务 (Backend)**
   - FastAPI 框架
   - SQLModel ORM
   - Alembic 数据库迁移
   - Supabase 认证集成

2. **前端应用 (Frontend)**
   - React + TypeScript
   - Vite 构建工具
   - Tailwind CSS 样式框架
   - Zustand 状态管理

3. **PDF服务 (PDF Service)**
   - FastAPI 框架
   - Docling 文档转换
   - PyMuPDF 文档解析

4. **Supabase 服务**
   - PostgreSQL 数据库
   - 认证服务
   - 存储服务
   - 实时更新

## 安装部署指南

### 安装步骤

#### 1. 克隆代码库并安装 uv

```bash
git clone https://******/3stooges-portal-v2.git
cd 3stooges-portal-v2

# 安装 uv (如果尚未安装)
pip install uv
```

#### 2. 启动 Supabase 服务

```bash
# 安装Supabase CLI
# 确保您在项目根目录
supabase init

supabase start
```

#### 3. 初始化数据库

启动 Supabase 服务后，需要初始化数据库结构：

```bash
cd backend
# 使用 uv 包管理器
uv run alembic upgrade head

# 在supabase后台创建两个storage(public): diagrams, documents
# 并设置policy
```

#### 4. 安装和启动后端服务

```bash
# 确保您在 backend 目录中
cd backend  # 如果您不在 backend 目录中

# 安装依赖
uv sync

# 启动后端服务
uv run uvicorn app.main:app --port 8000 --reload
```

后端服务将在 http://localhost:8000 上运行，API 文档可在 http://localhost:8000/docs 访问。

#### 5. 安装和启动 PDF 服务

打开一个新的终端窗口：

```bash
cd pdf_service

# 安装依赖
uv sync

# 启动 PDF 服务
uv run uvicorn src.main:app --reload --port 8002
```

PDF 服务将在 http://localhost:8002 上运行。

#### 6. 安装和启动前端服务

打开一个新的终端窗口：

```bash
cd frontend  # 从项目根目录进入前端目录

# 使用 pnpm（推荐）
npm install -g pnpm
pnpm install

# 启动前端服务
pnpm dev
```

前端服务将在 http://localhost:80 上运行。

#### 7. 验证安装

安装完成后，您可以通过以下方式验证系统是否正常工作：

1. 访问 Supabase Studio: http://localhost:54323
2. 访问后端 API 文档: http://localhost:8000/docs
3. 访问 PDF 服务: http://localhost:8002/docs
4. 访问前端应用: http://localhost:8080

## 各组件说明

### 后端服务 (Backend)

后端服务基于 FastAPI 框架，提供 RESTful API 接口，主要功能包括：

- 用户认证与授权（通过 Supabase）
- 数据库操作（使用 SQLModel ORM）
- 文件管理
- AI 模型集成
- 搜索引擎集成

API 文档可在 http://localhost:8000/docs 访问。

主要环境变量：
```
SUPABASE_URL=http://localhost:54321
SUPABASE_KEY=your-supabase-key
SUPABASE_DB_STRING=postgresql://postgres:postgres@localhost:54322/postgres
```

### 前端应用 (Frontend)

前端应用基于 React + TypeScript + Vite，提供用户界面，主要功能包括：

- 用户登录与注册
- 文档管理与查看
- 知识库管理
- AI 对话
- 图表生成
- 学术微博


## 生产环境部署

对于生产环境，我们提供了 Docker Compose 配置：

```bash
# 在项目根目录下
docker compose up -d --build
```

这将构建并启动所有服务，包括：
- 后端 API 服务
- 前端 Web 应用
- PDF 处理服务
- Supabase 服务


## 常见问题解决

### 端口冲突

如果遇到端口冲突，可以检查哪些进程占用了相关端口：

```bash
# 在 macOS/Linux 上
netstat -tuln | grep '80\|8000\|8002\|54321\|54322\|54323'
```

然后修改相应服务的端口配置。

### 数据库连接问题

如果后端无法连接到数据库，请确认：

1. Supabase 服务是否正在运行：`supabase status`
2. 数据库连接字符串是否正确：
   ```
   SUPABASE_DB_STRING=postgresql://postgres:postgres@localhost:54322/postgres
   ```

### 更多资源

- [Supabase 设置详情](./SUPABASE_SETUP.md)
- [数据库迁移指南](./backend/DATABASE_MIGRATION.md)
- [ngrok]

```bash
  ngrok config add-authtoken *************************************************
  ngrok http http://localhost:8080
```


## 功能列表

- [x] 用户认证与授权
  - [x] 登录/注册
  - [x] 用户管理
  - [ ] 团队管理

- [x] 知识库管理
  - [x] 论文库管理与上传
  - [x] 论文库搜索与摘要
  - [x] PDF预览
  - [ ] 个人知识图谱

- [x] AI 对话
  - [x] 多模型支持
  - [x] 对话历史管理
  - [x] 对话内容导出
  - [x] 搜索结果集成

- [x] 图表生成
  - [x] Mermaid 流程图
  - [x] PlantUML 图表
  - [x] 思维过程可视化

- [x] 学术微博
  - [x] 发布与评论
  - [x] 文摘库集成
  - [x] AI 对话内容分享

- [x] 搜索引擎集成
  - [x] 多引擎支持 (Tavily, Bing, Exa等)
  - [x] 搜索结果转换为Markdown
  - [x] 用户管理引擎Key

- [x] 系统管理
  - [x] 个人统计页面
  - [x] 模型管理
  - [x] 搜索引擎管理
  - [x] 存储设置


## TODO Functions
- [x] UI
  - [x] 主界面响应式布局
  - [x] NaviBar
  - [x] 工具箱
  - [x] 对话组件（chatInput）
  - [x] 个人中心
  - [x] Header
  - [x] Footer
  - [x] toast提示

- [x] Supabase框架
  - [x] Docker部署
  - [x] 数据库支持
  - [ ] Vector

- [x] LLM 集成
  - [x] 使用数据库管理
  - [x] 各种模型Endpoint统一管理
    - [x] 模型管理GUI
    - [x] 模型管理实现
    - [x] 各种服务模型的OpenAI兼容接口重构

- [x] 授权
  - [x] 后台
  - [x] 登录/注册
  - [ ] register页面添加Avatar图标
  - [ ] 团队与用户管理
    - [ ] 个人管理
    - [ ] 团队管理

- [x] Academic 对话
  - [x] 随时加入搜索结果到对话中
  - [x] 加入知识库文档到对话中
  - [x] 对话内容copy
  - [x] 支持对话历史管理
    - [ ] 对话历史存储数据库（有bug）
    - [x] 对话历史页面
  - [x] 对话管理
    - [x] 首页搜索问题
    - [x] 首页QA的逻辑问题（不能搜索，不能添加知识库）
    - [x] 移除express server
    - [x] 文档转换后的图片和文档滚动问题
    - [x] 随想在添加一点想法的时候没有设限制，应该根据用户的权限(发现后端已经过滤，前端刷在后台存储刷新有问题)
    - [ ] 终止对话
  - [x] 对话记录导出
    - [x] Word
    - [ ] Markdown
    - [ ] Obsidian

- [ ] Agent管理
  - [x] Agent切换
  - [ ] 追问：““通过上下文或最后一段对话来猜测用户可能进一步追问的3个相关问题””
  - [ ] Agent数据库功能
  - [ ] Prompt智能生成
  - [ ] Prompt智能管理
  - [ ] MCP support
    - [ ] client

  - [ ] Agentic memory
    - [ ] Mem0

- [x] 搜索引擎（前端+数据库）
  - [x] Search Engine(数据库管理)
  - [x] 支持（Tivaly，SerpAPI、Bing、Exa, Serper.dev）
    - [x] 搜索结果的精准性
    - [x] 搜索到的url转换为Markdown
  - [ ] 额度不足自动切换
  - [x] 用户管理引擎Key（加密）

- [x] 知识库
  - [ ] 个人知识图谱
    - [ ] 个人知识图谱管理(React Flow)
    - [ ] 个人知识图谱上传
    - [ ] 个人知识图谱管理
    - [ ] 个人知识图谱搜索
    - [ ] 个人知识图谱摘要
    - [ ] 个人知识图谱内容
    - [ ] 个人知识图谱图片
    - [ ] 个人知识图谱图片描述
  - [x] 论文库
      - [x] 论文库管理
      - [x] 论文库上传
      - [x] 论文库管理
      - [x] 论文库搜索
      - [x] 论文库摘要
      - [x] 论文库内容
      - [x] 论文库图片
      - [ ] 论文库图片描述
      - [x] PDF预览
  - [x] 统一知识库后台服务
    - [ ] MinerU服务与部署(重写部署)
      - [ ] 服务部署
        - [ ] paddlepaddle-gpu(1 p/s)
        - [ ] 每次启动生成随机Key
        - [ ] 缓冲处理（遍历，检查是否转换过）
      - [x] 前台集成
    - [x] Docling文档转换后台服务
        - [x] TOC后台
        - [x] PDF转Markdown（Docling后台）
    - [ ] 个人知识库向量化存储
      - [ ] Abstract向量化存储
      - [ ] 全文本向量化存储
        - [ ] 文本分割存储
        - [ ] 界面文本选择
    - [x] 论文的图片显示
    - [x] 论文图片提取和存储
      - [ ] 图片描述完善（目前是无意义的描述，需要从原文智能提取图片描述）
    - [ ] 文档split(markdown section)
    - [x] 大模型阅读分析论文
    - [ ] 相似性检索对话框（查询用和加入到对话中用）

- [x] 图库
  - [x] 图库存储、管理与检索
  - [x] 图库分页
  - [x] 图片全屏显示
  - [ ] 基于视觉模型的学术用图总结(后台当前支持JPG格式)
  - [x] 实现学术图库服务
  - [x] 图库获取效率
    - [x] 添加拇指图（不需要了，存储在Supabase）
    - [x] 图库thumbnail图生成
  - [x] 图库按user_id管理

- [x] 学术Weibo
    - [x] posts
    - [x] 文摘库（集成到微博）
      - [x] 页面解析
        - [x] jina.reader
        - [x] firecrawl
    - [x] 前台
    - [x] comments
    - [x] AI对话
    - [x] AI对话内容选择后直接评论
    - [x] 编辑
    - [x] 删除
    - [ ] 点赞
    - [ ] @他人

- [x] 个人灵感记录（Bookmark）
  - [x] 灵感——雁过留痕
    - [x] 专用页面管理
  - [x] 灵感——添加
  - [x] 提供更新功能
  - [x] 灵感——内容选择，集中便捷添加
  - [x] 雁过留痕详细书签页
    - [x] 可pinned

- [x] 系统管理
  - [x] 个人统计页面（Dashboard）没有自动定位刷新
  - [x] 设置页面
    - [x] 个人信息
    - [x] 模型管理
    - [x] 搜索引擎管理
    - [x] 存储设置
    - [x] Agent管理
    - [x] 外部服务的Health状态
  - [x] Agent管理
    - [ ] Prompt存储
  - [] Team
    - [ ] 知识库开放和私有化
    - [ ] 组长/Leader发布的TODO list的推荐阅读
    - [ ] 用户交互痕迹完整列表
    - [ ] 科研用图的描述更新
    - [ ] 向量化服务的健康性检测（知识库的向量化按钮）

- [x] 其他（Killer tool-kits）
  - [x] 学术研究思维循环链
  - [x] think过程的可视化
  - [x] 学术用图生成
    - [x] PlantUML
    - [x] Madrid流程图
    - [x] 对话内容直接生成
    - [ ] 生成图存储与管理
  - [x] 大模型阅读分析论文
    - [x] 论文Section分割
    - [x] 论文Section步骤处理
    - [x] 论文Section界面
    - [x] 论文Section总结
  - [ ] LlamaResearcher（https://github.com/AstraBert/llama-4-researcher）
  - [ ] Prompt生成
  - [ ] 一键生成综述

- [x] 部署
  - [x] Supabase
  - [x] Docker Compose

## 其他
    - [ ] Chrome Extension

