# 局域网访问修复说明

## 问题描述

在使用 Docker 部署 3Stooges Portal 时，局域网用户访问会遇到以下问题：

1. **微博中的图片无法显示**：图片 URL 包含硬编码的 `localhost` 或 `127.0.0.1`
2. **微博中的附件无法打开**：附件链接指向本地地址
3. **图库列表显示不正确**：图片路径转换错误

## 根本原因

程序中存在硬编码的 URL 地址，包括：
- `localhost:54321` (Supabase 存储服务)
- `127.0.0.1:54321` (Supabase 存储服务)
- `host.docker.internal:54321` (Docker 内部地址)

这些地址在局域网环境中无法被其他机器访问。

## 修复方案

### 1. URL 转换工具优化

**文件**: `frontend/src/utils/url-utils.ts`

**修改内容**:
- 动态获取当前主机地址，而不是硬编码 `localhost`
- 将所有存储 URL 转换为通过 Nginx 代理的路径 `/storage-proxy/`
- 支持局域网访问的 URL 转换

**关键改进**:
```typescript
// 修改前：硬编码 localhost
return url.replace('host.docker.internal:54321', 'localhost:54321');

// 修改后：使用当前主机地址和代理路径
const currentHost = getCurrentHost();
return url.replace('http://host.docker.internal:54321/storage', `http://${currentHost}/storage-proxy`);
```

### 2. Nginx 配置增强

**文件**: `docker/nginx/nginx.conf`

**修改内容**:
- 添加环境变量支持，允许配置 Supabase 主机地址
- 增强存储代理配置，支持 CORS 跨域访问
- 添加预检请求处理

**关键改进**:
```nginx
# 支持环境变量配置
set $supabase_host "${SUPABASE_HOST}";
if ($supabase_host = "") {
    set $supabase_host "host.docker.internal";
}

proxy_pass http://$supabase_host:54321/storage/;
```

### 3. Docker 配置优化

**文件**: `docker/nginx/Dockerfile`

**修改内容**:
- 添加 `envsubst` 工具支持环境变量替换
- 创建启动脚本处理配置模板
- 支持动态配置生成

### 4. 环境变量配置

**新增文件**: `.env.example`

提供配置模板，支持：
- 本地开发环境配置
- 局域网访问配置
- 生产环境配置

## 使用方法

### 本地开发环境

无需额外配置，使用默认设置即可：

```bash
./docker-start.sh --build
```

### 局域网访问环境

1. **复制配置文件**:
   ```bash
   cp .env.example .env
   ```

2. **编辑配置文件**:
   ```bash
   # 编辑 .env 文件
   SUPABASE_HOST=*************  # 替换为运行 Supabase 的机器 IP
   ```

3. **重新构建并启动**:
   ```bash
   # 开发环境
   ./docker-start.sh --build
   
   # 或生产环境
   ./docker-start-prod.sh --build
   ```

4. **访问应用**:
   - 本机访问: `http://localhost:8080`
   - 局域网访问: `http://*************:8080` (替换为实际 IP)

## 技术细节

### URL 转换流程

1. **后端返回原始 URL**: `http://host.docker.internal:54321/storage/v1/object/public/...`
2. **前端转换处理**: 使用 `convertSupabaseUrl()` 函数转换
3. **最终 URL**: `http://*************:8080/storage-proxy/v1/object/public/...`
4. **Nginx 代理**: 将请求转发到实际的 Supabase 服务

### 环境变量支持

- `SUPABASE_HOST`: Supabase 服务主机地址
  - 默认值: `host.docker.internal`
  - 局域网示例: `*************`
  - 生产环境示例: `supabase.example.com`

### 兼容性

- ✅ 本地开发环境
- ✅ 局域网访问
- ✅ 生产环境部署
- ✅ Docker 容器化部署
- ✅ 跨域访问支持

## 验证方法

1. **检查图片显示**: 微博中的图片应该正常显示
2. **测试附件下载**: 微博附件应该可以正常打开
3. **验证图库功能**: 图库列表应该正确显示图片
4. **跨设备访问**: 局域网内其他设备应该能正常访问

## 故障排除

### 图片仍然无法显示

1. 检查 `.env` 文件配置是否正确
2. 确认 Supabase 服务正在运行
3. 检查防火墙设置，确保 54321 端口可访问
4. 查看浏览器开发者工具的网络请求

### 局域网无法访问

1. 确认 Docker 服务正在运行: `docker ps`
2. 检查端口映射: `docker port stooges-nginx`
3. 验证网络连接: `ping <服务器IP>`
4. 检查防火墙设置

### 配置不生效

1. 重新构建容器: `./docker-start-prod.sh --build`
2. 检查环境变量: `docker exec stooges-nginx env | grep SUPABASE`
3. 查看 Nginx 配置: `docker exec stooges-nginx cat /etc/nginx/conf.d/default.conf`

## 相关文件

- `frontend/src/utils/url-utils.ts` - URL 转换工具
- `docker/nginx/nginx.conf` - Nginx 配置模板
- `docker/nginx/Dockerfile` - Nginx 容器配置
- `docker/docker-compose.yml` - 生产环境 Docker 配置
- `.env.example` - 环境变量配置模板
- `docker-start-prod.sh` - 生产环境启动脚本
