#!/bin/bash

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
  echo -e "${2}${1}${NC}"
}

# 检查命令是否存在
check_command() {
  if ! command -v $1 &> /dev/null; then
    print_message "错误: $1 命令未找到，请先安装" "$RED"
    exit 1
  fi
}

# 检查必要的命令
check_command "docker"
check_command "docker-compose"

# 加载环境变量
load_env() {
  if [ -f ".env" ]; then
    print_message "加载环境变量文件 .env" "$YELLOW"
    export $(cat .env | grep -v '^#' | xargs)
  else
    print_message "未找到 .env 文件，使用默认配置" "$YELLOW"
    print_message "如需自定义配置，请复制 .env.example 为 .env 并修改" "$BLUE"
  fi
}

# 检查局域网配置
check_network_config() {
  if [ -n "$SUPABASE_HOST" ] && [ "$SUPABASE_HOST" != "host.docker.internal" ]; then
    print_message "检测到局域网配置: SUPABASE_HOST=$SUPABASE_HOST" "$BLUE"
    print_message "确保以下条件满足:" "$YELLOW"
    print_message "  1. Supabase 在 $SUPABASE_HOST:54321 上运行" "$BLUE"
    print_message "  2. 防火墙允许访问 54321 端口" "$BLUE"
    print_message "  3. 网络连接正常" "$BLUE"
  fi
}

# 显示帮助信息
show_help() {
  print_message "3Stooges Portal 生产环境 Docker 部署工具" "$BLUE"
  echo ""
  print_message "用法:" "$YELLOW"
  echo "  ./docker-start-prod.sh [选项]"
  echo ""
  print_message "选项:" "$YELLOW"
  echo "  --help, -h       显示帮助信息"
  echo "  --build, -b      构建并启动生产环境服务"
  echo "  --start, -s      启动生产环境服务（不重新构建）"
  echo "  --stop, -d       停止服务"
  echo "  --restart, -r    重启服务"
  echo "  --logs, -l       查看日志"
  echo "  --status, -t     查看服务状态"
  echo ""
  print_message "局域网访问配置:" "$YELLOW"
  echo "  1. 复制配置文件: cp .env.example .env"
  echo "  2. 编辑 .env 文件，设置 SUPABASE_HOST 为 Supabase 服务器的 IP 地址"
  echo "  3. 重新构建并启动: ./docker-start-prod.sh --build"
  echo ""
  print_message "注意:" "$RED"
  echo "  此脚本使用 docker/docker-compose.yml 配置文件"
  echo "  适用于生产环境部署，包含 Nginx 反向代理"
  echo ""
}

# 检查 Supabase 是否运行
check_supabase() {
  print_message "检查 Supabase 服务状态..." "$YELLOW"
  
  # 确定要检查的主机
  SUPABASE_CHECK_HOST=${SUPABASE_HOST:-"localhost"}
  if [ "$SUPABASE_CHECK_HOST" = "host.docker.internal" ]; then
    SUPABASE_CHECK_HOST="localhost"
  fi
  
  # 尝试连接到 Supabase 端口
  if nc -z $SUPABASE_CHECK_HOST 54321 &>/dev/null; then
    print_message "✅ Supabase 服务已运行在 $SUPABASE_CHECK_HOST:54321" "$GREEN"
    return 0
  else
    print_message "⚠️ Supabase 服务未运行在 $SUPABASE_CHECK_HOST:54321" "$RED"
    print_message "请先启动 Supabase 服务:" "$YELLOW"
    print_message "  supabase start" "$BLUE"
    
    read -p "是否继续部署？(y/n): " continue_deploy
    if [[ "$continue_deploy" != "y" && "$continue_deploy" != "Y" ]]; then
      print_message "部署已取消" "$RED"
      exit 1
    fi
    
    print_message "⚠️ 警告: 在没有 Supabase 的情况下继续部署，应用可能无法正常工作" "$YELLOW"
    return 1
  fi
}

# 构建并启动服务
build_and_start() {
  print_message "构建并启动生产环境 Docker 服务..." "$YELLOW"
  cd docker
  docker-compose up -d --build
  cd ..
  
  if [ $? -eq 0 ]; then
    print_message "✅ 生产环境服务已成功启动" "$GREEN"
    print_message "应用可通过以下地址访问:" "$BLUE"
    print_message "  前端: http://localhost:8080" "$BLUE"
    print_message "  后端 API: http://localhost:8080/api/" "$BLUE"
    print_message "  PDF 服务: http://localhost:8080/pdf-service/" "$BLUE"
    print_message "  存储代理: http://localhost:8080/storage-proxy/" "$BLUE"
    echo ""
    print_message "局域网访问:" "$BLUE"
    print_message "  将 localhost 替换为本机 IP 地址即可在局域网内访问" "$BLUE"
  else
    print_message "❌ 服务启动失败，请检查错误信息" "$RED"
  fi
}

# 启动服务（不重新构建）
start_services() {
  print_message "启动生产环境 Docker 服务..." "$YELLOW"
  cd docker
  docker-compose up -d
  cd ..
  
  if [ $? -eq 0 ]; then
    print_message "✅ 生产环境服务已成功启动" "$GREEN"
    print_message "应用可通过以下地址访问:" "$BLUE"
    print_message "  前端: http://localhost:8080" "$BLUE"
    print_message "  后端 API: http://localhost:8080/api/" "$BLUE"
    print_message "  PDF 服务: http://localhost:8080/pdf-service/" "$BLUE"
    print_message "  存储代理: http://localhost:8080/storage-proxy/" "$BLUE"
  else
    print_message "❌ 服务启动失败，请检查错误信息" "$RED"
  fi
}

# 停止服务
stop_services() {
  print_message "停止生产环境 Docker 服务..." "$YELLOW"
  cd docker
  docker-compose down
  cd ..
  
  if [ $? -eq 0 ]; then
    print_message "✅ 服务已成功停止" "$GREEN"
  else
    print_message "❌ 服务停止失败，请检查错误信息" "$RED"
  fi
}

# 重启服务
restart_services() {
  print_message "重启生产环境 Docker 服务..." "$YELLOW"
  cd docker
  docker-compose restart
  cd ..
  
  if [ $? -eq 0 ]; then
    print_message "✅ 服务已成功重启" "$GREEN"
  else
    print_message "❌ 服务重启失败，请检查错误信息" "$RED"
  fi
}

# 查看日志
view_logs() {
  print_message "查看生产环境服务日志..." "$YELLOW"
  cd docker
  docker-compose logs -f
  cd ..
}

# 查看服务状态
check_status() {
  print_message "生产环境服务状态:" "$YELLOW"
  cd docker
  docker-compose ps
  cd ..
}

# 主函数
main() {
  # 如果没有参数，显示帮助信息
  if [ $# -eq 0 ]; then
    show_help
    exit 0
  fi
  
  # 处理参数
  case "$1" in
    --help|-h)
      show_help
      ;;
    --build|-b)
      load_env
      check_network_config
      check_supabase
      build_and_start
      ;;
    --start|-s)
      load_env
      check_network_config
      check_supabase
      start_services
      ;;
    --stop|-d)
      stop_services
      ;;
    --restart|-r)
      restart_services
      ;;
    --logs|-l)
      view_logs
      ;;
    --status|-t)
      check_status
      ;;
    *)
      print_message "未知选项: $1" "$RED"
      show_help
      exit 1
      ;;
  esac
}

# 执行主函数
main "$@"
