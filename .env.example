# 3Stooges Portal 环境变量配置示例
# 复制此文件为 .env 并根据您的环境进行配置

# ===========================================
# 局域网访问配置
# ===========================================

# Supabase 主机地址配置
# 对于本地开发：使用 host.docker.internal（默认）
# 对于局域网访问：使用运行 Supabase 的机器的 IP 地址
# 例如：SUPABASE_HOST=*************
SUPABASE_HOST=host.docker.internal

# ===========================================
# 部署说明
# ===========================================

# 1. 本地开发环境（默认配置）
#    - 使用默认值即可，无需修改
#    - Supabase 运行在同一台机器上

# 2. 局域网访问环境
#    - 将 SUPABASE_HOST 设置为运行 Supabase 的机器的 IP 地址
#    - 例如：SUPABASE_HOST=*************
#    - 确保防火墙允许访问 54321 端口

# 3. 生产环境
#    - 根据实际的 Supabase 部署地址进行配置
#    - 可能需要使用域名或公网 IP

# ===========================================
# 使用方法
# ===========================================

# 1. 复制此文件：cp .env.example .env
# 2. 根据您的环境修改配置
# 3. 重新构建并启动 Docker 容器：
#    ./docker-start.sh --build
