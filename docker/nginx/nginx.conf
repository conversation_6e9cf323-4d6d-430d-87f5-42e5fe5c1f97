server {
    listen 80;
    server_name _;

    # 启用 sub_filter 模块
    sub_filter_types *;

    # 添加 MIME 类型
    types {
        text/html                             html htm shtml;
        text/css                              css;
        application/javascript                js;
        text/javascript                       mjs;
        application/json                      json;
        image/jpeg                            jpeg jpg;
        image/png                             png;
        image/gif                             gif;
        image/svg+xml                         svg svgz;
        image/webp                            webp;
        font/woff                             woff;
        font/woff2                            woff2;
        application/pdf                       pdf;
    }

    # 启用访问日志
    access_log /var/log/nginx/access.log;
    error_log /var/log/nginx/error.log debug;

    # Frontend static files
    location / {
        root /usr/share/nginx/html;
        index index.html index.htm;
        try_files $uri $uri/ /index.html;

        # 设置正确的内容类型
        default_type text/html;

        # 禁用缓存
        add_header Cache-Control "no-store, no-cache, must-revalidate, proxy-revalidate";
        add_header Pragma "no-cache";
        expires 0;

        # 替换硬编码的 URL
        sub_filter "http://localhost:8000/v1/api" "/api";
        sub_filter "http://localhost:8000" "";

        # 替换 Supabase 存储 URL
        sub_filter "http://127.0.0.1:54321/storage" "/storage-proxy";
        sub_filter "http://host.docker.internal:54321/storage" "/storage-proxy";

        # 在 HTML 中添加 PDF.js worker 脚本
        sub_filter "</head>" "<script>
            // 重写 importScripts 函数，使其使用 CDN 版本的 PDF.js worker 脚本
            window.pdfjsWorkerSrc = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/5.1.91/pdf.worker.min.js';

            // 监听 PDF.js worker 脚本的加载
            window.addEventListener('DOMContentLoaded', function() {
                // 如果 PDF.js 尝试加载 worker 脚本，重定向到 CDN 版本
                const originalFetch = window.fetch;
                window.fetch = function(url, options) {
                    if (url && typeof url === 'string' && url.includes('pdf.worker.min-B9SsWq0g.mjs')) {
                        console.log('Redirecting PDF.js worker request to CDN');
                        return originalFetch(window.pdfjsWorkerSrc, options);
                    }
                    return originalFetch.apply(this, arguments);
                };
            });
        </script></head>";

        sub_filter_once off;
    }

    # 明确设置 HTML 文件的 MIME 类型
    location ~ \.html$ {
        root /usr/share/nginx/html;
        add_header Content-Type "text/html; charset=utf-8";
        expires 0;
    }

    # Backend API proxy
    location /api/ {
        proxy_pass http://backend:8000/v1/api/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # 处理硬编码的 localhost:8000 URL
    location /v1/api/ {
        proxy_pass http://backend:8000/v1/api/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # 处理硬编码的 localhost:8000/v1/api/ URL
    location = /v1/api/auth/sign_in {
        proxy_pass http://backend:8000/v1/api/auth/sign_in;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }



    # PDF Service proxy
    location /pdf-service/ {
        proxy_pass http://pdf_service:8002/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # Supabase Storage proxy
    # 支持局域网访问，使用环境变量配置Supabase主机地址
    location /storage-proxy/ {
        # 使用环境变量 SUPABASE_HOST，默认为 host.docker.internal
        set $supabase_host "${SUPABASE_HOST}";
        if ($supabase_host = "") {
            set $supabase_host "host.docker.internal";
        }

        proxy_pass http://$supabase_host:54321/storage/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;

        # 添加CORS头以支持跨域访问
        add_header 'Access-Control-Allow-Origin' '*' always;
        add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS, PUT, DELETE' always;
        add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization' always;

        # 处理预检请求
        if ($request_method = 'OPTIONS') {
            add_header 'Access-Control-Allow-Origin' '*';
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS, PUT, DELETE';
            add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization';
            add_header 'Access-Control-Max-Age' 1728000;
            add_header 'Content-Type' 'text/plain; charset=utf-8';
            add_header 'Content-Length' 0;
            return 204;
        }
    }

    # Increase max body size for file uploads
    client_max_body_size 100M;
}
