FROM nginx:alpine

# Install envsubst for environment variable substitution
RUN apk add --no-cache gettext

# Remove default nginx config
RUN rm /etc/nginx/conf.d/default.conf

# Copy custom nginx config template
COPY nginx.conf /etc/nginx/templates/default.conf.template

# Create directory for frontend files
RUN mkdir -p /usr/share/nginx/html/assets

# Copy PDF.js worker script
COPY pdf.worker.min-B9SsWq0g.mjs /usr/share/nginx/html/assets/

# Create startup script
RUN echo '#!/bin/sh' > /docker-entrypoint.sh && \
    echo 'envsubst < /etc/nginx/templates/default.conf.template > /etc/nginx/conf.d/default.conf' >> /docker-entrypoint.sh && \
    echo 'exec nginx -g "daemon off;"' >> /docker-entrypoint.sh && \
    chmod +x /docker-entrypoint.sh

# Set default environment variables
ENV SUPABASE_HOST=host.docker.internal

# Expose port 80
EXPOSE 80

CMD ["/docker-entrypoint.sh"]
