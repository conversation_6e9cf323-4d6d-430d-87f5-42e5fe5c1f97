#!/bin/bash

# 颜色定义
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_message() {
  echo -e "${2}${1}${NC}"
}

print_message "3Stooges Portal 局域网访问修复验证脚本" "$BLUE"
echo ""

# 检查修复的文件是否存在
print_message "检查修复文件..." "$YELLOW"

files_to_check=(
  "frontend/src/utils/url-utils.ts"
  "docker/nginx/nginx.conf"
  "docker/nginx/Dockerfile"
  "docker/docker-compose.yml"
  ".env.example"
  "docker-start-prod.sh"
  "NETWORK_FIX.md"
)

all_files_exist=true
for file in "${files_to_check[@]}"; do
  if [ -f "$file" ]; then
    print_message "✅ $file" "$GREEN"
  else
    print_message "❌ $file 不存在" "$RED"
    all_files_exist=false
  fi
done

if [ "$all_files_exist" = false ]; then
  print_message "❌ 部分文件缺失，请检查修复是否完整" "$RED"
  exit 1
fi

echo ""
print_message "检查关键修复内容..." "$YELLOW"

# 检查 URL 工具函数是否包含动态主机获取
if grep -q "getCurrentHost" frontend/src/utils/url-utils.ts; then
  print_message "✅ URL 工具函数包含动态主机获取功能" "$GREEN"
else
  print_message "❌ URL 工具函数缺少动态主机获取功能" "$RED"
fi

# 检查 Nginx 配置是否支持环境变量
if grep -q "SUPABASE_HOST" docker/nginx/nginx.conf; then
  print_message "✅ Nginx 配置支持环境变量" "$GREEN"
else
  print_message "❌ Nginx 配置缺少环境变量支持" "$RED"
fi

# 检查 Docker 配置是否包含环境变量
if grep -q "SUPABASE_HOST" docker/docker-compose.yml; then
  print_message "✅ Docker 配置包含环境变量支持" "$GREEN"
else
  print_message "❌ Docker 配置缺少环境变量支持" "$RED"
fi

# 检查下载工具是否使用 URL 转换
if grep -q "convertUrlForDocker\|convertSupabaseUrl" frontend/src/utils/download.ts; then
  print_message "✅ 下载工具使用 URL 转换功能" "$GREEN"
else
  print_message "❌ 下载工具缺少 URL 转换功能" "$RED"
fi

echo ""
print_message "检查脚本权限..." "$YELLOW"

if [ -x "docker-start-prod.sh" ]; then
  print_message "✅ 生产环境启动脚本有执行权限" "$GREEN"
else
  print_message "⚠️ 生产环境启动脚本缺少执行权限，正在修复..." "$YELLOW"
  chmod +x docker-start-prod.sh
  print_message "✅ 已添加执行权限" "$GREEN"
fi

echo ""
print_message "修复验证完成！" "$BLUE"
echo ""
print_message "使用说明:" "$YELLOW"
echo "1. 本地开发环境："
echo "   ./docker-start.sh --build"
echo ""
echo "2. 局域网访问环境："
echo "   cp .env.example .env"
echo "   # 编辑 .env 文件，设置 SUPABASE_HOST=<Supabase服务器IP>"
echo "   ./docker-start-prod.sh --build"
echo ""
echo "3. 验证修复效果："
echo "   - 检查微博图片是否正常显示"
echo "   - 测试微博附件是否可以打开"
echo "   - 验证图库列表是否正确显示"
echo "   - 在局域网其他设备上访问应用"
echo ""
print_message "详细说明请查看 NETWORK_FIX.md 文件" "$BLUE"
